#!/usr/bin/env node

/**
 * AirForShare Web - Local Network Sharing Tool
 * Main server entry point
 */

const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const cron = require('node-cron');

// Import custom modules
const config = require('./src/config');
const logger = require('./src/utils/logger');
const deviceDiscovery = require('./src/services/discovery');
const fileManager = require('./src/services/fileManager');
const authService = require('./src/services/auth');
const cleanupService = require('./src/services/cleanup');

// Import routes
const apiRoutes = require('./src/routes/api');
const fileRoutes = require('./src/routes/files');
const authRoutes = require('./src/routes/auth');

class AirForShareServer {
    constructor() {
        this.app = express();
        this.server = http.createServer(this.app);
        this.io = socketIo(this.server, {
            cors: {
                origin: "*",
                methods: ["GET", "POST"]
            }
        });
        this.port = config.server.port;
        this.setupMiddleware();
        this.setupRoutes();
        this.setupSocketIO();
        this.setupCleanup();
    }

    setupMiddleware() {
        // Security middleware
        this.app.use(helmet({
            contentSecurityPolicy: false, // Allow inline scripts for local app
            crossOriginEmbedderPolicy: false
        }));
        
        // CORS for local network access
        this.app.use(cors({
            origin: true,
            credentials: true
        }));

        // Compression
        this.app.use(compression());

        // Rate limiting
        const limiter = rateLimit({
            windowMs: 15 * 60 * 1000, // 15 minutes
            max: 100, // Limit each IP to 100 requests per windowMs
            message: 'Too many requests from this IP'
        });
        this.app.use('/api/', limiter);

        // Body parsing
        this.app.use(express.json({ limit: config.upload.maxFileSize }));
        this.app.use(express.urlencoded({ extended: true, limit: config.upload.maxFileSize }));

        // Static files
        this.app.use(express.static(path.join(__dirname, 'public')));
        
        // Logging middleware
        this.app.use((req, res, next) => {
            logger.info(`${req.method} ${req.url} - ${req.ip}`);
            next();
        });
    }

    setupRoutes() {
        // API routes
        this.app.use('/api', apiRoutes);
        this.app.use('/api/files', fileRoutes);
        this.app.use('/api/auth', authRoutes);

        // Main app route
        this.app.get('/', (req, res) => {
            res.sendFile(path.join(__dirname, 'public', 'index.html'));
        });

        // Health check
        this.app.get('/health', (req, res) => {
            res.json({
                status: 'ok',
                timestamp: new Date().toISOString(),
                uptime: process.uptime(),
                version: require('./package.json').version
            });
        });

        // 404 handler
        this.app.use((req, res) => {
            res.status(404).json({ error: 'Not found' });
        });

        // Error handler
        this.app.use((err, req, res, next) => {
            logger.error('Server error:', err);
            res.status(500).json({ error: 'Internal server error' });
        });
    }

    setupSocketIO() {
        this.io.on('connection', (socket) => {
            logger.info(`Client connected: ${socket.id}`);

            // Send current device list
            socket.emit('devices', deviceDiscovery.getDevices());

            // Handle device discovery events
            socket.on('discover', () => {
                socket.emit('devices', deviceDiscovery.getDevices());
            });

            // Handle text sharing
            socket.on('shareText', (data) => {
                logger.info('Text shared:', data.text.substring(0, 50) + '...');
                socket.broadcast.emit('textReceived', {
                    text: data.text,
                    sender: data.sender || 'Anonymous',
                    timestamp: new Date().toISOString()
                });
            });

            // Handle disconnect
            socket.on('disconnect', () => {
                logger.info(`Client disconnected: ${socket.id}`);
            });
        });

        // Listen for device discovery events
        deviceDiscovery.on('deviceFound', (device) => {
            this.io.emit('deviceFound', device);
        });

        deviceDiscovery.on('deviceLost', (device) => {
            this.io.emit('deviceLost', device);
        });
    }

    setupCleanup() {
        // Run cleanup every 5 minutes
        cron.schedule('*/5 * * * *', () => {
            cleanupService.cleanupExpiredFiles();
        });

        // Cleanup on startup
        cleanupService.cleanupExpiredFiles();
    }

    async start() {
        try {
            // Initialize services
            await fileManager.initialize();
            await deviceDiscovery.start();

            // Start server
            this.server.listen(this.port, '0.0.0.0', () => {
                logger.info(`AirForShare Web server started on port ${this.port}`);
                logger.info(`Access the application at:`);
                logger.info(`  Local: http://localhost:${this.port}`);
                
                // Get and display local IP addresses
                const networkInterfaces = require('os').networkInterfaces();
                Object.keys(networkInterfaces).forEach(interfaceName => {
                    networkInterfaces[interfaceName].forEach(interface => {
                        if (interface.family === 'IPv4' && !interface.internal) {
                            logger.info(`  Network: http://${interface.address}:${this.port}`);
                        }
                    });
                });
            });

        } catch (error) {
            logger.error('Failed to start server:', error);
            process.exit(1);
        }
    }

    async stop() {
        logger.info('Shutting down AirForShare Web server...');
        
        try {
            await deviceDiscovery.stop();
            this.server.close();
            logger.info('Server shutdown complete');
        } catch (error) {
            logger.error('Error during shutdown:', error);
        }
    }
}

// Create and start server
const server = new AirForShareServer();

// Handle graceful shutdown
process.on('SIGINT', async () => {
    logger.info('Received SIGINT, shutting down gracefully...');
    await server.stop();
    process.exit(0);
});

process.on('SIGTERM', async () => {
    logger.info('Received SIGTERM, shutting down gracefully...');
    await server.stop();
    process.exit(0);
});

// Start the server
server.start().catch(error => {
    logger.error('Failed to start server:', error);
    process.exit(1);
});

module.exports = server;
