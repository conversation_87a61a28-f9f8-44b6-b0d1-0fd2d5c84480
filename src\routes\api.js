/**
 * Main API routes
 */

const express = require('express');
const router = express.Router();
const deviceDiscovery = require('../services/discovery');
const fileManager = require('../services/fileManager');
const authService = require('../services/auth');
const cleanupService = require('../services/cleanup');
const config = require('../config');
const logger = require('../utils/logger');

// Get server status and info
router.get('/status', (req, res) => {
    try {
        const deviceInfo = deviceDiscovery.getOwnDevice();
        const fileStats = fileManager.getFileStats();
        const cleanupStats = cleanupService.getCleanupStats();

        res.json({
            status: 'online',
            device: deviceInfo,
            files: fileStats,
            cleanup: cleanupStats,
            config: {
                maxFileSize: config.upload.maxFileSize,
                maxSimultaneousUploads: config.upload.maxSimultaneousUploads,
                expiryTime: config.cleanup.expiryTime,
                privateMode: config.privateMode.enabled
            },
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.error('Error getting status:', error);
        res.status(500).json({ error: 'Failed to get status' });
    }
});

// Get discovered devices
router.get('/devices', (req, res) => {
    try {
        const devices = deviceDiscovery.getDevices();
        res.json({
            devices: devices,
            count: devices.length,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.error('Error getting devices:', error);
        res.status(500).json({ error: 'Failed to get devices' });
    }
});

// Refresh device discovery
router.post('/devices/refresh', (req, res) => {
    try {
        deviceDiscovery.refresh();
        res.json({ message: 'Device discovery refreshed' });
    } catch (error) {
        logger.error('Error refreshing devices:', error);
        res.status(500).json({ error: 'Failed to refresh devices' });
    }
});

// Get file list
router.get('/files', (req, res) => {
    try {
        const files = fileManager.getFileList();
        res.json({
            files: files,
            count: files.length,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.error('Error getting file list:', error);
        res.status(500).json({ error: 'Failed to get file list' });
    }
});

// Text sharing endpoints
const textItems = new Map(); // In-memory storage for text items

// Share text
router.post('/text/share', (req, res) => {
    try {
        const { text, title, sender } = req.body;

        if (!text || text.length > config.textSharing.maxLength) {
            return res.status(400).json({ 
                error: `Text is required and must be less than ${config.textSharing.maxLength} characters` 
            });
        }

        const textId = require('uuid').v4();
        const textItem = {
            id: textId,
            text: text,
            title: title || 'Shared Text',
            sender: sender || 'Anonymous',
            createdAt: new Date().toISOString(),
            expiresAt: new Date(Date.now() + config.textSharing.expiryTime * 60 * 1000).toISOString(),
            accessCount: 0
        };

        textItems.set(textId, textItem);

        // Broadcast to all connected clients
        req.app.get('io').emit('textShared', {
            id: textId,
            title: textItem.title,
            sender: textItem.sender,
            createdAt: textItem.createdAt
        });

        logger.info(`Text shared: ${textItem.title} by ${textItem.sender}`);

        res.json({
            id: textId,
            message: 'Text shared successfully',
            expiresAt: textItem.expiresAt
        });

    } catch (error) {
        logger.error('Error sharing text:', error);
        res.status(500).json({ error: 'Failed to share text' });
    }
});

// Get shared text
router.get('/text/:id', (req, res) => {
    try {
        const textId = req.params.id;
        const textItem = textItems.get(textId);

        if (!textItem) {
            return res.status(404).json({ error: 'Text not found' });
        }

        // Check if expired
        if (new Date() > new Date(textItem.expiresAt)) {
            textItems.delete(textId);
            return res.status(404).json({ error: 'Text has expired' });
        }

        // Update access info
        textItem.accessCount++;
        textItem.expiresAt = new Date(Date.now() + config.textSharing.expiryTime * 60 * 1000).toISOString();

        res.json(textItem);

    } catch (error) {
        logger.error('Error getting text:', error);
        res.status(500).json({ error: 'Failed to get text' });
    }
});

// Get list of shared texts
router.get('/text', (req, res) => {
    try {
        const now = new Date();
        const validTexts = [];

        // Clean up expired texts and collect valid ones
        for (const [id, textItem] of textItems.entries()) {
            if (now > new Date(textItem.expiresAt)) {
                textItems.delete(id);
            } else {
                validTexts.push({
                    id: textItem.id,
                    title: textItem.title,
                    sender: textItem.sender,
                    createdAt: textItem.createdAt,
                    accessCount: textItem.accessCount
                });
            }
        }

        res.json({
            texts: validTexts,
            count: validTexts.length,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        logger.error('Error getting text list:', error);
        res.status(500).json({ error: 'Failed to get text list' });
    }
});

// Delete shared text
router.delete('/text/:id', (req, res) => {
    try {
        const textId = req.params.id;
        
        if (textItems.has(textId)) {
            textItems.delete(textId);
            res.json({ message: 'Text deleted successfully' });
        } else {
            res.status(404).json({ error: 'Text not found' });
        }

    } catch (error) {
        logger.error('Error deleting text:', error);
        res.status(500).json({ error: 'Failed to delete text' });
    }
});

// Manual cleanup trigger (admin only)
router.post('/cleanup', authService.authenticateMiddleware(), authService.authorizeMiddleware('admin'), async (req, res) => {
    try {
        await cleanupService.runFullCleanup();
        res.json({ message: 'Cleanup completed successfully' });
    } catch (error) {
        logger.error('Error running cleanup:', error);
        res.status(500).json({ error: 'Failed to run cleanup' });
    }
});

// Get configuration (public info only)
router.get('/config', (req, res) => {
    try {
        res.json({
            upload: {
                maxFileSize: config.upload.maxFileSize,
                maxSimultaneousUploads: config.upload.maxSimultaneousUploads,
                allowedTypes: config.upload.allowedTypes,
                blockedTypes: config.upload.blockedTypes
            },
            textSharing: {
                maxLength: config.textSharing.maxLength,
                expiryTime: config.textSharing.expiryTime
            },
            cleanup: {
                expiryTime: config.cleanup.expiryTime
            },
            privateMode: {
                enabled: config.privateMode.enabled
            }
        });
    } catch (error) {
        logger.error('Error getting config:', error);
        res.status(500).json({ error: 'Failed to get configuration' });
    }
});

module.exports = router;
