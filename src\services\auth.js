/**
 * Authentication and authorization service
 */

const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { v4: uuidv4 } = require('uuid');
const config = require('../config');
const logger = require('../utils/logger');

class AuthService {
    constructor() {
        this.users = new Map();
        this.sessions = new Map();
        this.privateRooms = new Map();
        this.initialize();
    }

    initialize() {
        // Create default admin user
        this.createUser(
            config.auth.defaultAdmin.username,
            config.auth.defaultAdmin.password,
            'admin'
        );

        logger.info('Authentication service initialized');
    }

    async createUser(username, password, userType = 'registered') {
        try {
            if (this.users.has(username)) {
                throw new Error('User already exists');
            }

            const hashedPassword = await bcrypt.hash(password, 10);
            const user = {
                id: uuidv4(),
                username: username,
                password: hashedPassword,
                userType: userType,
                createdAt: new Date().toISOString(),
                lastLogin: null,
                isActive: true
            };

            this.users.set(username, user);
            logger.info(`User created: ${username} (${userType})`);
            
            return { id: user.id, username: user.username, userType: user.userType };

        } catch (error) {
            logger.error('Error creating user:', error);
            throw error;
        }
    }

    async authenticateUser(username, password) {
        try {
            const user = this.users.get(username);
            if (!user || !user.isActive) {
                throw new Error('Invalid credentials');
            }

            const isValidPassword = await bcrypt.compare(password, user.password);
            if (!isValidPassword) {
                throw new Error('Invalid credentials');
            }

            // Update last login
            user.lastLogin = new Date().toISOString();

            // Generate JWT token
            const token = jwt.sign(
                { 
                    userId: user.id, 
                    username: user.username, 
                    userType: user.userType 
                },
                config.auth.jwtSecret,
                { expiresIn: config.auth.jwtExpiration }
            );

            logger.info(`User authenticated: ${username}`);
            
            return {
                token: token,
                user: {
                    id: user.id,
                    username: user.username,
                    userType: user.userType
                }
            };

        } catch (error) {
            logger.error('Authentication error:', error);
            throw error;
        }
    }

    verifyToken(token) {
        try {
            const decoded = jwt.verify(token, config.auth.jwtSecret);
            const user = Array.from(this.users.values()).find(u => u.id === decoded.userId);
            
            if (!user || !user.isActive) {
                throw new Error('User not found or inactive');
            }

            return {
                userId: user.id,
                username: user.username,
                userType: user.userType
            };

        } catch (error) {
            throw new Error('Invalid token');
        }
    }

    createGuestSession() {
        const sessionId = uuidv4();
        const session = {
            id: sessionId,
            userType: 'guest',
            createdAt: new Date().toISOString(),
            lastActivity: new Date().toISOString()
        };

        this.sessions.set(sessionId, session);
        
        // Generate guest token
        const token = jwt.sign(
            { 
                sessionId: sessionId,
                userType: 'guest'
            },
            config.auth.jwtSecret,
            { expiresIn: config.auth.jwtExpiration }
        );

        logger.info(`Guest session created: ${sessionId}`);
        
        return {
            token: token,
            sessionId: sessionId,
            userType: 'guest'
        };
    }

    verifyGuestSession(token) {
        try {
            const decoded = jwt.verify(token, config.auth.jwtSecret);
            
            if (decoded.userType !== 'guest') {
                throw new Error('Not a guest session');
            }

            const session = this.sessions.get(decoded.sessionId);
            if (!session) {
                throw new Error('Session not found');
            }

            // Update last activity
            session.lastActivity = new Date().toISOString();

            return {
                sessionId: session.id,
                userType: 'guest'
            };

        } catch (error) {
            throw new Error('Invalid guest session');
        }
    }

    // Private room management
    createPrivateRoom(password, createdBy) {
        const roomId = uuidv4();
        const hashedPassword = bcrypt.hashSync(password, 10);
        
        const room = {
            id: roomId,
            password: hashedPassword,
            createdBy: createdBy,
            createdAt: new Date().toISOString(),
            expiresAt: new Date(Date.now() + config.privateMode.room.roomExpiry).toISOString(),
            users: new Set(),
            files: new Set(),
            textItems: new Set()
        };

        this.privateRooms.set(roomId, room);
        logger.info(`Private room created: ${roomId} by ${createdBy}`);
        
        return roomId;
    }

    async joinPrivateRoom(roomId, password, userId) {
        const room = this.privateRooms.get(roomId);
        if (!room) {
            throw new Error('Room not found');
        }

        // Check if room has expired
        if (new Date() > new Date(room.expiresAt)) {
            this.privateRooms.delete(roomId);
            throw new Error('Room has expired');
        }

        // Verify password
        const isValidPassword = await bcrypt.compare(password, room.password);
        if (!isValidPassword) {
            throw new Error('Invalid room password');
        }

        // Check room capacity
        if (room.users.size >= config.privateMode.room.maxUsersPerRoom) {
            throw new Error('Room is full');
        }

        room.users.add(userId);
        logger.info(`User ${userId} joined private room ${roomId}`);
        
        return room;
    }

    leavePrivateRoom(roomId, userId) {
        const room = this.privateRooms.get(roomId);
        if (room) {
            room.users.delete(userId);
            logger.info(`User ${userId} left private room ${roomId}`);
            
            // Delete room if empty
            if (room.users.size === 0) {
                this.privateRooms.delete(roomId);
                logger.info(`Private room ${roomId} deleted (empty)`);
            }
        }
    }

    getPrivateRoom(roomId) {
        return this.privateRooms.get(roomId);
    }

    // Middleware for authentication
    authenticateMiddleware() {
        return (req, res, next) => {
            const authHeader = req.headers.authorization;
            
            if (!authHeader || !authHeader.startsWith('Bearer ')) {
                return res.status(401).json({ error: 'No token provided' });
            }

            const token = authHeader.substring(7);

            try {
                // Try to verify as user token first
                try {
                    const user = this.verifyToken(token);
                    req.user = user;
                    req.userType = user.userType;
                    return next();
                } catch (userError) {
                    // Try to verify as guest session
                    const session = this.verifyGuestSession(token);
                    req.session = session;
                    req.userType = 'guest';
                    return next();
                }
            } catch (error) {
                return res.status(401).json({ error: 'Invalid token' });
            }
        };
    }

    // Middleware for authorization
    authorizeMiddleware(requiredUserType) {
        return (req, res, next) => {
            const userTypeHierarchy = { guest: 0, registered: 1, admin: 2 };
            const requiredLevel = userTypeHierarchy[requiredUserType] || 0;
            const userLevel = userTypeHierarchy[req.userType] || 0;

            if (userLevel < requiredLevel) {
                return res.status(403).json({ error: 'Insufficient permissions' });
            }

            next();
        };
    }

    // Cleanup expired sessions and rooms
    cleanup() {
        const now = new Date();
        
        // Cleanup expired private rooms
        for (const [roomId, room] of this.privateRooms.entries()) {
            if (now > new Date(room.expiresAt)) {
                this.privateRooms.delete(roomId);
                logger.info(`Expired private room deleted: ${roomId}`);
            }
        }

        // Cleanup old guest sessions (older than 24 hours)
        const sessionExpiry = 24 * 60 * 60 * 1000; // 24 hours
        for (const [sessionId, session] of this.sessions.entries()) {
            if (now.getTime() - new Date(session.lastActivity).getTime() > sessionExpiry) {
                this.sessions.delete(sessionId);
                logger.info(`Expired guest session deleted: ${sessionId}`);
            }
        }
    }
}

// Create singleton instance
const authService = new AuthService();

module.exports = authService;
