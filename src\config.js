/**
 * Configuration settings for AirForShare Web
 */

const path = require('path');

const config = {
    // Server configuration
    server: {
        port: process.env.PORT || 3000,
        host: '0.0.0.0',
        name: process.env.DEVICE_NAME || require('os').hostname()
    },

    // File upload configuration
    upload: {
        // File size limits by user type (in bytes)
        maxFileSize: {
            guest: 10 * 1024 * 1024,      // 10MB for guests
            registered: 100 * 1024 * 1024, // 100MB for registered users
            admin: 1024 * 1024 * 1024      // 1GB for admins
        },
        
        // Simultaneous upload limits by user type
        maxSimultaneousUploads: {
            guest: 2,
            registered: 5,
            admin: 10
        },

        // Storage directory
        storageDir: path.join(__dirname, '..', 'uploads'),
        
        // Allowed file types (empty array means all types allowed)
        allowedTypes: [],
        
        // Blocked file types for security
        blockedTypes: ['.exe', '.bat', '.cmd', '.com', '.scr', '.vbs', '.js', '.jar']
    },

    // Cleanup configuration
    cleanup: {
        // Time in minutes after last access before file is deleted
        expiryTime: 30,
        
        // How often to run cleanup (in minutes)
        cleanupInterval: 5
    },

    // Device discovery configuration
    discovery: {
        serviceName: '_airforshare-web._tcp',
        servicePort: 3000,
        
        // mDNS/Bonjour settings
        multicast: {
            address: '***********',
            port: 5353
        }
    },

    // Authentication configuration
    auth: {
        // JWT secret (should be changed in production)
        jwtSecret: process.env.JWT_SECRET || 'airforshare-web-secret-change-me',
        
        // JWT expiration time
        jwtExpiration: '24h',
        
        // Session configuration
        session: {
            secret: process.env.SESSION_SECRET || 'airforshare-session-secret',
            maxAge: 24 * 60 * 60 * 1000 // 24 hours
        },

        // Default admin credentials (should be changed on first run)
        defaultAdmin: {
            username: 'admin',
            password: 'admin123'
        }
    },

    // Private mode configuration
    privateMode: {
        // Enable private rooms with password protection
        enabled: true,
        
        // Encryption settings
        encryption: {
            algorithm: 'aes-256-gcm',
            keyLength: 32,
            ivLength: 16
        },
        
        // Room settings
        room: {
            maxRooms: 10,
            maxUsersPerRoom: 20,
            roomExpiry: 60 * 60 * 1000 // 1 hour
        }
    },

    // Text and link sharing configuration
    textSharing: {
        // Maximum text length (in characters)
        maxLength: 10000,
        
        // Text expiry time (in minutes)
        expiryTime: 30,
        
        // Maximum number of text items per user
        maxItemsPerUser: 50
    },

    // Logging configuration
    logging: {
        level: process.env.LOG_LEVEL || 'info',
        file: path.join(__dirname, '..', 'logs', 'airforshare-web.log'),
        maxSize: '10m',
        maxFiles: 5
    },

    // Security configuration
    security: {
        // Enable HTTPS (requires certificates)
        https: {
            enabled: false,
            keyFile: path.join(__dirname, '..', 'certs', 'key.pem'),
            certFile: path.join(__dirname, '..', 'certs', 'cert.pem')
        },
        
        // CORS settings
        cors: {
            origin: true,
            credentials: true
        },
        
        // Rate limiting
        rateLimit: {
            windowMs: 15 * 60 * 1000, // 15 minutes
            max: 100 // requests per window
        }
    },

    // Development settings
    development: {
        // Enable debug mode
        debug: process.env.NODE_ENV !== 'production',
        
        // Mock device discovery for testing
        mockDiscovery: false,
        
        // Disable cleanup for testing
        disableCleanup: false
    }
};

// Environment-specific overrides
if (process.env.NODE_ENV === 'production') {
    config.logging.level = 'warn';
    config.development.debug = false;
}

// Validate configuration
function validateConfig() {
    // Ensure storage directory exists
    const fs = require('fs');
    if (!fs.existsSync(config.upload.storageDir)) {
        fs.mkdirSync(config.upload.storageDir, { recursive: true });
    }

    // Ensure logs directory exists
    const logsDir = path.dirname(config.logging.file);
    if (!fs.existsSync(logsDir)) {
        fs.mkdirSync(logsDir, { recursive: true });
    }

    // Validate file size limits
    Object.keys(config.upload.maxFileSize).forEach(userType => {
        if (config.upload.maxFileSize[userType] <= 0) {
            throw new Error(`Invalid file size limit for ${userType}`);
        }
    });

    // Validate expiry times
    if (config.cleanup.expiryTime <= 0) {
        throw new Error('Invalid cleanup expiry time');
    }

    if (config.textSharing.expiryTime <= 0) {
        throw new Error('Invalid text sharing expiry time');
    }
}

// Run validation
validateConfig();

module.exports = config;
