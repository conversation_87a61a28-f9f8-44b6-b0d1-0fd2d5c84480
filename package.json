{"name": "airforshare-web", "version": "1.0.0", "description": "Web-based local network sharing tool for files, text, and links", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build": "pkg . --out-path dist/", "build:docker": "docker build -t airforshare-web .", "test": "jest", "lint": "eslint ."}, "keywords": ["file-sharing", "local-network", "offline", "web-app", "cross-platform"], "author": "AirForShare Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.2", "multer": "^1.4.5-lts.1", "bonjour": "^3.5.0", "crypto": "^1.0.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.0.0", "compression": "^1.7.4", "express-rate-limit": "^6.10.0", "uuid": "^9.0.0", "mime-types": "^2.1.35", "sanitize-filename": "^1.6.3", "node-cron": "^3.0.2"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "eslint": "^8.45.0", "pkg": "^5.8.1"}, "pkg": {"scripts": ["server.js", "public/**/*", "views/**/*"], "assets": ["public/**/*", "views/**/*"], "targets": ["node16-win-x64", "node16-linux-x64", "node16-macos-x64"]}, "engines": {"node": ">=16.0.0"}}