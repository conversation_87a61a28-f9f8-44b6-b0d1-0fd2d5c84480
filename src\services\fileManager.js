/**
 * File management service for handling uploads, downloads, and cleanup
 */

const fs = require('fs').promises;
const fsSync = require('fs');
const path = require('path');
const crypto = require('crypto');
const { v4: uuidv4 } = require('uuid');
const sanitizeFilename = require('sanitize-filename');
const mime = require('mime-types');
const config = require('../config');
const logger = require('../utils/logger');

class FileManager {
    constructor() {
        this.files = new Map(); // In-memory file metadata storage
        this.storageDir = config.upload.storageDir;
        this.initialized = false;
    }

    async initialize() {
        if (this.initialized) {
            return;
        }

        try {
            // Ensure storage directory exists
            await fs.mkdir(this.storageDir, { recursive: true });
            
            // Load existing files from storage
            await this.loadExistingFiles();
            
            this.initialized = true;
            logger.info('File manager initialized successfully');

        } catch (error) {
            logger.error('Failed to initialize file manager:', error);
            throw error;
        }
    }

    async loadExistingFiles() {
        try {
            const files = await fs.readdir(this.storageDir);
            
            for (const filename of files) {
                if (filename.endsWith('.meta')) {
                    const metaPath = path.join(this.storageDir, filename);
                    const metaContent = await fs.readFile(metaPath, 'utf8');
                    const metadata = JSON.parse(metaContent);
                    
                    // Check if the actual file exists
                    const filePath = path.join(this.storageDir, metadata.filename);
                    if (fsSync.existsSync(filePath)) {
                        this.files.set(metadata.id, metadata);
                    } else {
                        // Remove orphaned metadata
                        await fs.unlink(metaPath);
                    }
                }
            }

            logger.info(`Loaded ${this.files.size} existing files`);

        } catch (error) {
            logger.error('Error loading existing files:', error);
        }
    }

    async saveFile(fileBuffer, originalName, userType = 'guest', userId = 'anonymous') {
        try {
            // Validate file
            this.validateFile(originalName, fileBuffer.length, userType);

            // Generate unique filename and ID
            const fileId = uuidv4();
            const sanitizedName = sanitizeFilename(originalName);
            const fileExtension = path.extname(sanitizedName);
            const filename = `${fileId}${fileExtension}`;
            const filePath = path.join(this.storageDir, filename);

            // Calculate file hash
            const hash = crypto.createHash('sha256').update(fileBuffer).digest('hex');

            // Create file metadata
            const metadata = {
                id: fileId,
                originalName: sanitizedName,
                filename: filename,
                size: fileBuffer.length,
                mimeType: mime.lookup(sanitizedName) || 'application/octet-stream',
                hash: hash,
                uploadedBy: userId,
                userType: userType,
                uploadedAt: new Date().toISOString(),
                lastAccessed: new Date().toISOString(),
                accessCount: 0,
                expiresAt: new Date(Date.now() + config.cleanup.expiryTime * 60 * 1000).toISOString()
            };

            // Save file to disk
            await fs.writeFile(filePath, fileBuffer);

            // Save metadata
            await this.saveMetadata(metadata);

            // Store in memory
            this.files.set(fileId, metadata);

            logger.info(`File saved: ${originalName} (${fileId}) by ${userId}`);
            return metadata;

        } catch (error) {
            logger.error('Error saving file:', error);
            throw error;
        }
    }

    async getFile(fileId) {
        try {
            const metadata = this.files.get(fileId);
            if (!metadata) {
                throw new Error('File not found');
            }

            // Check if file has expired
            if (new Date() > new Date(metadata.expiresAt)) {
                await this.deleteFile(fileId);
                throw new Error('File has expired');
            }

            const filePath = path.join(this.storageDir, metadata.filename);
            
            // Check if file exists on disk
            if (!fsSync.existsSync(filePath)) {
                this.files.delete(fileId);
                throw new Error('File not found on disk');
            }

            // Update access information
            metadata.lastAccessed = new Date().toISOString();
            metadata.accessCount++;
            metadata.expiresAt = new Date(Date.now() + config.cleanup.expiryTime * 60 * 1000).toISOString();

            // Save updated metadata
            await this.saveMetadata(metadata);

            // Read file content
            const fileBuffer = await fs.readFile(filePath);

            return {
                metadata: metadata,
                buffer: fileBuffer
            };

        } catch (error) {
            logger.error(`Error getting file ${fileId}:`, error);
            throw error;
        }
    }

    async deleteFile(fileId) {
        try {
            const metadata = this.files.get(fileId);
            if (!metadata) {
                return false;
            }

            const filePath = path.join(this.storageDir, metadata.filename);
            const metaPath = path.join(this.storageDir, `${fileId}.meta`);

            // Delete file and metadata from disk
            try {
                await fs.unlink(filePath);
            } catch (error) {
                // File might not exist, continue
            }

            try {
                await fs.unlink(metaPath);
            } catch (error) {
                // Metadata might not exist, continue
            }

            // Remove from memory
            this.files.delete(fileId);

            logger.info(`File deleted: ${metadata.originalName} (${fileId})`);
            return true;

        } catch (error) {
            logger.error(`Error deleting file ${fileId}:`, error);
            return false;
        }
    }

    async saveMetadata(metadata) {
        const metaPath = path.join(this.storageDir, `${metadata.id}.meta`);
        await fs.writeFile(metaPath, JSON.stringify(metadata, null, 2));
    }

    validateFile(filename, size, userType) {
        // Check file size limits
        const maxSize = config.upload.maxFileSize[userType] || config.upload.maxFileSize.guest;
        if (size > maxSize) {
            throw new Error(`File size exceeds limit for ${userType} users (${Math.round(maxSize / 1024 / 1024)}MB)`);
        }

        // Check blocked file types
        const extension = path.extname(filename).toLowerCase();
        if (config.upload.blockedTypes.includes(extension)) {
            throw new Error(`File type ${extension} is not allowed`);
        }

        // Check allowed file types (if specified)
        if (config.upload.allowedTypes.length > 0 && !config.upload.allowedTypes.includes(extension)) {
            throw new Error(`File type ${extension} is not allowed`);
        }
    }

    getFileList(userId = null, userType = null) {
        const files = Array.from(this.files.values());
        
        // Filter by user if specified
        if (userId) {
            return files.filter(file => file.uploadedBy === userId);
        }

        // Return public file list (without sensitive info)
        return files.map(file => ({
            id: file.id,
            originalName: file.originalName,
            size: file.size,
            mimeType: file.mimeType,
            uploadedAt: file.uploadedAt,
            accessCount: file.accessCount
        }));
    }

    getFileStats() {
        const files = Array.from(this.files.values());
        const totalSize = files.reduce((sum, file) => sum + file.size, 0);
        
        return {
            totalFiles: files.length,
            totalSize: totalSize,
            totalSizeMB: Math.round(totalSize / 1024 / 1024 * 100) / 100
        };
    }

    async cleanupExpiredFiles() {
        const now = new Date();
        const expiredFiles = [];

        for (const [fileId, metadata] of this.files.entries()) {
            if (now > new Date(metadata.expiresAt)) {
                expiredFiles.push(fileId);
            }
        }

        let deletedCount = 0;
        for (const fileId of expiredFiles) {
            if (await this.deleteFile(fileId)) {
                deletedCount++;
            }
        }

        if (deletedCount > 0) {
            logger.info(`Cleaned up ${deletedCount} expired files`);
        }

        return deletedCount;
    }
}

// Create singleton instance
const fileManager = new FileManager();

module.exports = fileManager;
