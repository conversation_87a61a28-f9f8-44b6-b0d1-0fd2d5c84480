/**
 * Logging utility for AirForShare Web
 */

const fs = require('fs');
const path = require('path');
const config = require('../config');

class Logger {
    constructor() {
        this.logLevels = {
            error: 0,
            warn: 1,
            info: 2,
            debug: 3
        };
        
        this.currentLevel = this.logLevels[config.logging.level] || this.logLevels.info;
        this.logFile = config.logging.file;
        
        // Ensure log directory exists
        const logDir = path.dirname(this.logFile);
        if (!fs.existsSync(logDir)) {
            fs.mkdirSync(logDir, { recursive: true });
        }
    }

    formatMessage(level, message, ...args) {
        const timestamp = new Date().toISOString();
        const formattedArgs = args.length > 0 ? ' ' + args.map(arg => 
            typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
        ).join(' ') : '';
        
        return `[${timestamp}] ${level.toUpperCase()}: ${message}${formattedArgs}`;
    }

    writeToFile(formattedMessage) {
        try {
            fs.appendFileSync(this.logFile, formattedMessage + '\n');
        } catch (error) {
            console.error('Failed to write to log file:', error);
        }
    }

    log(level, message, ...args) {
        const levelValue = this.logLevels[level];
        
        if (levelValue <= this.currentLevel) {
            const formattedMessage = this.formatMessage(level, message, ...args);
            
            // Write to console
            if (level === 'error') {
                console.error(formattedMessage);
            } else if (level === 'warn') {
                console.warn(formattedMessage);
            } else {
                console.log(formattedMessage);
            }
            
            // Write to file
            this.writeToFile(formattedMessage);
        }
    }

    error(message, ...args) {
        this.log('error', message, ...args);
    }

    warn(message, ...args) {
        this.log('warn', message, ...args);
    }

    info(message, ...args) {
        this.log('info', message, ...args);
    }

    debug(message, ...args) {
        this.log('debug', message, ...args);
    }

    // Log rotation (simple implementation)
    rotateLog() {
        try {
            const stats = fs.statSync(this.logFile);
            const maxSize = 10 * 1024 * 1024; // 10MB
            
            if (stats.size > maxSize) {
                const backupFile = this.logFile + '.old';
                fs.renameSync(this.logFile, backupFile);
                this.info('Log file rotated');
            }
        } catch (error) {
            // Ignore rotation errors
        }
    }
}

// Create singleton instance
const logger = new Logger();

// Rotate log on startup
logger.rotateLog();

module.exports = logger;
