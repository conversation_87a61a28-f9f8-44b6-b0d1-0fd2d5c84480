<!-- Use this file to provide workspace-specific custom instructions to <PERSON><PERSON><PERSON>. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->
- [x] Verify that the copilot-instructions.md file in the .github directory is created.

- [x] Clarify Project Requirements
    <!-- Python-based AirDrop-like file sharing tool with local network discovery, P2P transfer, and cross-platform GUI -->

- [x] Scaffold the Project
    <!-- Created main.py, src/ structure with core/, gui/, utils/ modules, requirements.txt, README.md -->

- [x] Customize the Project
    <!-- Implemented device discovery with Zeroconf, HTTP file server, and Tkinter GUI with drag-drop interface -->

- [x] Install Required Extensions
    <!-- No extensions needed -->

- [x] Compile the Project
    <!-- Created virtual environment, installed dependencies, fixed syntax errors -->

- [ ] Create and Run Task

- [ ] Launch the Project

- [ ] Ensure Documentation is Complete

## Project: AirForShare - Local Network File Sharing Tool

This is a Python-based AirDrop-like file sharing application that enables:
- Local network discovery of devices
- Peer-to-peer file transfer
- Cross-platform support (Windows, macOS, Linux)
- Offline access within private networks
- Simple drag-and-drop interface
