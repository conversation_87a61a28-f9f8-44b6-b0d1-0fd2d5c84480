"""
Main GUI window for AirForShare application
Provides drag-and-drop interface and device discovery
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
from pathlib import Path
import logging
from typing import Dict, List

class MainWindow:
    """Main application window with drag-and-drop file sharing interface"""
    
    def __init__(self, root: tk.Tk, discovery, file_server):
        self.root = root
        self.discovery = discovery
        self.file_server = file_server
        self.logger = logging.getLogger(__name__)
        
        # Device tracking
        self.discovered_devices: Dict[str, dict] = {}
        
        # Setup callbacks
        self.discovery.add_device_callback(self._on_device_changed)
        self.file_server.add_upload_callback(self._on_file_received)
        
        # Setup GUI
        self._setup_gui()
        self._setup_drag_drop()
        
        # Start periodic updates
        self._update_device_list()
    
    def _setup_gui(self):
        """Setup the main GUI layout"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="🚀 AirForShare", font=('Arial', 18, 'bold'))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # Device discovery section
        devices_frame = ttk.LabelFrame(main_frame, text="📱 Nearby Devices", padding="10")
        devices_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        devices_frame.columnconfigure(0, weight=1)
        devices_frame.rowconfigure(0, weight=1)
        
        # Device list
        self.device_tree = ttk.Treeview(devices_frame, columns=('address', 'platform'), height=6)
        self.device_tree.heading('#0', text='Device Name')
        self.device_tree.heading('address', text='IP Address')
        self.device_tree.heading('platform', text='Platform')
        
        self.device_tree.column('#0', width=200)
        self.device_tree.column('address', width=150)
        self.device_tree.column('platform', width=100)
        
        # Scrollbar for device list
        device_scrollbar = ttk.Scrollbar(devices_frame, orient=tk.VERTICAL, command=self.device_tree.yview)
        self.device_tree.configure(yscrollcommand=device_scrollbar.set)
        
        self.device_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        device_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # File transfer section
        transfer_frame = ttk.LabelFrame(main_frame, text="📁 File Transfer", padding="10")
        transfer_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        transfer_frame.columnconfigure(0, weight=1)
        transfer_frame.rowconfigure(1, weight=1)
        
        # Drag and drop area
        self.drop_frame = tk.Frame(transfer_frame, bg='lightgray', relief='groove', bd=2, height=150)
        self.drop_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        self.drop_frame.grid_propagate(False)
        
        drop_label = tk.Label(self.drop_frame, text="📎 Drag & Drop Files Here\nor Click to Browse", 
                             bg='lightgray', font=('Arial', 12), fg='gray')
        drop_label.place(relx=0.5, rely=0.5, anchor='center')
        
        # Browse button
        browse_btn = ttk.Button(transfer_frame, text="📂 Browse Files", command=self._browse_files)
        browse_btn.grid(row=1, column=0, pady=(0, 10))
        
        # Transfer log
        log_frame = ttk.LabelFrame(main_frame, text="📋 Transfer Log", padding="10")
        log_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.log_text = tk.Text(log_frame, height=8, wrap=tk.WORD)
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # Status bar
        self.status_var = tk.StringVar(value="Ready - Scanning for devices...")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # Initial log message
        self._log_message("🚀 AirForShare started - Ready to share files!")
    
    def _setup_drag_drop(self):
        """Setup drag and drop functionality"""
        # Bind click event for file browsing
        self.drop_frame.bind("<Button-1>", lambda e: self._browse_files())
        
        # Note: Full drag-and-drop requires additional libraries like tkinterdnd2
        # For now, we'll use file browsing as the primary method
        
    def _browse_files(self):
        """Open file browser dialog"""
        files = filedialog.askopenfilenames(
            title="Select files to share",
            filetypes=[
                ("All files", "*.*"),
                ("Images", "*.png *.jpg *.jpeg *.gif *.bmp"),
                ("Documents", "*.pdf *.doc *.docx *.txt"),
                ("Archives", "*.zip *.rar *.7z")
            ]
        )
        
        if files:
            self._handle_files(files)
    
    def _handle_files(self, file_paths: List[str]):
        """Handle selected files for sharing"""
        if not self.discovered_devices:
            messagebox.showwarning("No Devices", "No devices found. Make sure other devices are running AirForShare.")
            return
        
        # Show device selection dialog
        selected_device = self._select_target_device()
        if not selected_device:
            return
        
        # Send files to selected device
        for file_path in file_paths:
            self._send_file(file_path, selected_device)
    
    def _select_target_device(self) -> dict:
        """Show dialog to select target device"""
        if not self.discovered_devices:
            return None
        
        # Simple selection dialog
        device_names = list(self.discovered_devices.keys())
        if len(device_names) == 1:
            return self.discovered_devices[device_names[0]]
        
        # For multiple devices, use the selected item in the tree
        selection = self.device_tree.selection()
        if selection:
            item = self.device_tree.item(selection[0])
            device_name = item['text']
            return self.discovered_devices.get(device_name)
        
        messagebox.showinfo("Select Device", "Please select a device from the list first.")
        return None
    
    def _send_file(self, file_path: str, device_info: dict):
        """Send file to target device"""
        def send_thread():
            try:
                self._log_message(f"📤 Sending {Path(file_path).name} to {device_info['name']}...")
                
                success = self.file_server.send_file(
                    file_path, 
                    device_info['address'], 
                    device_info['port']
                )
                
                if success:
                    self._log_message(f"✅ Successfully sent {Path(file_path).name}")
                else:
                    self._log_message(f"❌ Failed to send {Path(file_path).name}")
                    
            except Exception as e:
                self.logger.error(f"Error sending file: {e}")
                self._log_message(f"❌ Error sending {Path(file_path).name}: {e}")
        
        # Send in background thread
        threading.Thread(target=send_thread, daemon=True).start()
    
    def _on_device_changed(self, device_info: dict, action: str):
        """Handle device discovery events"""
        def update_gui():
            if action == 'added':
                self.discovered_devices[device_info['name']] = device_info
                self._log_message(f"📱 Device found: {device_info['name']} ({device_info['address']})")
            elif action == 'removed':
                self.discovered_devices.pop(device_info['name'], None)
                self._log_message(f"📱 Device offline: {device_info['name']}")
            
            self._update_device_list()
            self._update_status()
        
        # Update GUI in main thread
        self.root.after(0, update_gui)
    
    def _on_file_received(self, file_info: dict):
        """Handle received file events"""
        def update_gui():
            sender = file_info.get('sender', 'Unknown')
            filename = file_info['name']
            self._log_message(f"📥 Received {filename} from {sender}")
            
            # Show notification
            messagebox.showinfo(
                "File Received", 
                f"Received file: {filename}\nFrom: {sender}\nSaved to: {file_info['path']}"
            )
        
        # Update GUI in main thread
        self.root.after(0, update_gui)
    
    def _update_device_list(self):
        """Update the device list display"""
        # Clear existing items
        for item in self.device_tree.get_children():
            self.device_tree.delete(item)
        
        # Add discovered devices
        for device_name, device_info in self.discovered_devices.items():
            self.device_tree.insert('', tk.END, 
                                  text=device_name,
                                  values=(device_info['address'], device_info['platform']))
    
    def _update_status(self):
        """Update status bar"""
        device_count = len(self.discovered_devices)
        if device_count == 0:
            self.status_var.set("Ready - Scanning for devices...")
        elif device_count == 1:
            self.status_var.set("Ready - 1 device found")
        else:
            self.status_var.set(f"Ready - {device_count} devices found")
    
    def _log_message(self, message: str):
        """Add message to transfer log"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.logger.info(message)
