/**
 * Device discovery service using mDNS/Bonjour
 */

const EventEmitter = require('events');
const bonjour = require('bonjour')();
const os = require('os');
const config = require('../config');
const logger = require('../utils/logger');

class DeviceDiscovery extends EventEmitter {
    constructor() {
        super();
        this.devices = new Map();
        this.service = null;
        this.browser = null;
        this.isRunning = false;
        this.deviceInfo = this.getDeviceInfo();
    }

    getDeviceInfo() {
        const networkInterfaces = os.networkInterfaces();
        let localIP = '127.0.0.1';
        
        // Find the first non-internal IPv4 address
        for (const interfaceName in networkInterfaces) {
            const interfaces = networkInterfaces[interfaceName];
            for (const iface of interfaces) {
                if (iface.family === 'IPv4' && !iface.internal) {
                    localIP = iface.address;
                    break;
                }
            }
            if (localIP !== '127.0.0.1') break;
        }

        return {
            name: config.server.name,
            host: localIP,
            port: config.server.port,
            type: 'airforshare-web',
            version: require('../../package.json').version,
            platform: os.platform(),
            arch: os.arch(),
            timestamp: Date.now()
        };
    }

    async start() {
        if (this.isRunning) {
            logger.warn('Device discovery is already running');
            return;
        }

        try {
            logger.info('Starting device discovery service...');

            // Publish our service
            this.service = bonjour.publish({
                name: this.deviceInfo.name,
                type: config.discovery.serviceName,
                port: this.deviceInfo.port,
                txt: {
                    version: this.deviceInfo.version,
                    platform: this.deviceInfo.platform,
                    arch: this.deviceInfo.arch,
                    type: this.deviceInfo.type,
                    timestamp: this.deviceInfo.timestamp.toString()
                }
            });

            logger.info(`Published service: ${this.deviceInfo.name} at ${this.deviceInfo.host}:${this.deviceInfo.port}`);

            // Start browsing for other services
            this.browser = bonjour.find({ type: config.discovery.serviceName }, (service) => {
                this.handleServiceFound(service);
            });

            // Handle service removal
            this.browser.on('down', (service) => {
                this.handleServiceLost(service);
            });

            this.isRunning = true;
            logger.info('Device discovery service started successfully');

        } catch (error) {
            logger.error('Failed to start device discovery:', error);
            throw error;
        }
    }

    async stop() {
        if (!this.isRunning) {
            return;
        }

        try {
            logger.info('Stopping device discovery service...');

            if (this.browser) {
                this.browser.stop();
                this.browser = null;
            }

            if (this.service) {
                this.service.stop();
                this.service = null;
            }

            this.devices.clear();
            this.isRunning = false;

            logger.info('Device discovery service stopped');

        } catch (error) {
            logger.error('Error stopping device discovery:', error);
        }
    }

    handleServiceFound(service) {
        try {
            // Skip our own service
            if (service.name === this.deviceInfo.name) {
                return;
            }

            const deviceId = `${service.name}_${service.host}_${service.port}`;
            
            // Check if we already know about this device
            if (this.devices.has(deviceId)) {
                return;
            }

            const device = {
                id: deviceId,
                name: service.name,
                host: service.host,
                port: service.port,
                type: service.txt?.type || 'unknown',
                version: service.txt?.version || 'unknown',
                platform: service.txt?.platform || 'unknown',
                arch: service.txt?.arch || 'unknown',
                lastSeen: Date.now(),
                url: `http://${service.host}:${service.port}`
            };

            this.devices.set(deviceId, device);
            
            logger.info(`Discovered device: ${device.name} at ${device.host}:${device.port}`);
            this.emit('deviceFound', device);

        } catch (error) {
            logger.error('Error handling discovered service:', error);
        }
    }

    handleServiceLost(service) {
        try {
            const deviceId = `${service.name}_${service.host}_${service.port}`;
            
            if (this.devices.has(deviceId)) {
                const device = this.devices.get(deviceId);
                this.devices.delete(deviceId);
                
                logger.info(`Lost device: ${device.name} at ${device.host}:${device.port}`);
                this.emit('deviceLost', device);
            }

        } catch (error) {
            logger.error('Error handling lost service:', error);
        }
    }

    getDevices() {
        return Array.from(this.devices.values());
    }

    getDevice(deviceId) {
        return this.devices.get(deviceId);
    }

    getDeviceCount() {
        return this.devices.size;
    }

    // Refresh device list (useful for manual refresh)
    refresh() {
        if (!this.isRunning) {
            logger.warn('Cannot refresh: device discovery is not running');
            return;
        }

        logger.info('Refreshing device list...');
        
        // Remove stale devices (not seen for more than 2 minutes)
        const staleThreshold = Date.now() - (2 * 60 * 1000);
        const staleDevices = [];

        for (const [deviceId, device] of this.devices.entries()) {
            if (device.lastSeen < staleThreshold) {
                staleDevices.push(deviceId);
            }
        }

        staleDevices.forEach(deviceId => {
            const device = this.devices.get(deviceId);
            this.devices.delete(deviceId);
            logger.info(`Removed stale device: ${device.name}`);
            this.emit('deviceLost', device);
        });

        // Emit current device list
        this.emit('devicesUpdated', this.getDevices());
    }

    // Get our own device info
    getOwnDevice() {
        return this.deviceInfo;
    }
}

// Create singleton instance
const deviceDiscovery = new DeviceDiscovery();

module.exports = deviceDiscovery;
