/**
 * Authentication routes
 */

const express = require('express');
const router = express.Router();
const authService = require('../services/auth');
const logger = require('../utils/logger');

// User login
router.post('/login', async (req, res) => {
    try {
        const { username, password } = req.body;

        if (!username || !password) {
            return res.status(400).json({ error: 'Username and password are required' });
        }

        const result = await authService.authenticateUser(username, password);
        
        res.json({
            message: 'Login successful',
            token: result.token,
            user: result.user
        });

    } catch (error) {
        logger.error('Login error:', error);
        res.status(401).json({ error: 'Invalid credentials' });
    }
});

// User registration
router.post('/register', async (req, res) => {
    try {
        const { username, password, confirmPassword } = req.body;

        if (!username || !password || !confirmPassword) {
            return res.status(400).json({ error: 'All fields are required' });
        }

        if (password !== confirmPassword) {
            return res.status(400).json({ error: 'Passwords do not match' });
        }

        if (password.length < 6) {
            return res.status(400).json({ error: 'Password must be at least 6 characters long' });
        }

        const user = await authService.createUser(username, password, 'registered');
        
        res.json({
            message: 'Registration successful',
            user: user
        });

    } catch (error) {
        logger.error('Registration error:', error);
        
        if (error.message === 'User already exists') {
            return res.status(409).json({ error: 'Username already exists' });
        }
        
        res.status(500).json({ error: 'Registration failed' });
    }
});

// Guest session creation
router.post('/guest', (req, res) => {
    try {
        const session = authService.createGuestSession();
        
        res.json({
            message: 'Guest session created',
            token: session.token,
            sessionId: session.sessionId,
            userType: session.userType
        });

    } catch (error) {
        logger.error('Guest session error:', error);
        res.status(500).json({ error: 'Failed to create guest session' });
    }
});

// Token verification
router.post('/verify', (req, res) => {
    try {
        const { token } = req.body;

        if (!token) {
            return res.status(400).json({ error: 'Token is required' });
        }

        try {
            // Try to verify as user token
            const user = authService.verifyToken(token);
            return res.json({
                valid: true,
                type: 'user',
                user: user
            });
        } catch (userError) {
            // Try to verify as guest session
            try {
                const session = authService.verifyGuestSession(token);
                return res.json({
                    valid: true,
                    type: 'guest',
                    session: session
                });
            } catch (sessionError) {
                return res.status(401).json({ 
                    valid: false, 
                    error: 'Invalid token' 
                });
            }
        }

    } catch (error) {
        logger.error('Token verification error:', error);
        res.status(500).json({ error: 'Token verification failed' });
    }
});

// Private room creation
router.post('/room/create', authService.authenticateMiddleware(), (req, res) => {
    try {
        const { password } = req.body;

        if (!password || password.length < 4) {
            return res.status(400).json({ error: 'Room password must be at least 4 characters long' });
        }

        const userId = req.user?.username || req.session?.sessionId || 'anonymous';
        const roomId = authService.createPrivateRoom(password, userId);

        res.json({
            message: 'Private room created',
            roomId: roomId
        });

    } catch (error) {
        logger.error('Room creation error:', error);
        res.status(500).json({ error: 'Failed to create private room' });
    }
});

// Join private room
router.post('/room/join', authService.authenticateMiddleware(), async (req, res) => {
    try {
        const { roomId, password } = req.body;

        if (!roomId || !password) {
            return res.status(400).json({ error: 'Room ID and password are required' });
        }

        const userId = req.user?.username || req.session?.sessionId || 'anonymous';
        const room = await authService.joinPrivateRoom(roomId, password, userId);

        res.json({
            message: 'Joined private room successfully',
            roomId: roomId,
            userCount: room.users.size
        });

    } catch (error) {
        logger.error('Room join error:', error);
        
        if (error.message === 'Room not found') {
            return res.status(404).json({ error: 'Room not found' });
        } else if (error.message === 'Room has expired') {
            return res.status(410).json({ error: 'Room has expired' });
        } else if (error.message === 'Invalid room password') {
            return res.status(401).json({ error: 'Invalid room password' });
        } else if (error.message === 'Room is full') {
            return res.status(429).json({ error: 'Room is full' });
        }
        
        res.status(500).json({ error: 'Failed to join room' });
    }
});

// Leave private room
router.post('/room/leave', authService.authenticateMiddleware(), (req, res) => {
    try {
        const { roomId } = req.body;

        if (!roomId) {
            return res.status(400).json({ error: 'Room ID is required' });
        }

        const userId = req.user?.username || req.session?.sessionId || 'anonymous';
        authService.leavePrivateRoom(roomId, userId);

        res.json({
            message: 'Left private room successfully'
        });

    } catch (error) {
        logger.error('Room leave error:', error);
        res.status(500).json({ error: 'Failed to leave room' });
    }
});

// Get room info
router.get('/room/:roomId', authService.authenticateMiddleware(), (req, res) => {
    try {
        const roomId = req.params.roomId;
        const room = authService.getPrivateRoom(roomId);

        if (!room) {
            return res.status(404).json({ error: 'Room not found' });
        }

        // Check if room has expired
        if (new Date() > new Date(room.expiresAt)) {
            return res.status(410).json({ error: 'Room has expired' });
        }

        res.json({
            id: room.id,
            createdBy: room.createdBy,
            createdAt: room.createdAt,
            expiresAt: room.expiresAt,
            userCount: room.users.size,
            fileCount: room.files.size,
            textCount: room.textItems.size
        });

    } catch (error) {
        logger.error('Room info error:', error);
        res.status(500).json({ error: 'Failed to get room info' });
    }
});

// Protected route example (requires authentication)
router.get('/profile', authService.authenticateMiddleware(), (req, res) => {
    try {
        if (req.user) {
            // Registered user
            res.json({
                type: 'user',
                user: req.user
            });
        } else if (req.session) {
            // Guest session
            res.json({
                type: 'guest',
                session: req.session
            });
        } else {
            res.status(401).json({ error: 'Not authenticated' });
        }

    } catch (error) {
        logger.error('Profile error:', error);
        res.status(500).json({ error: 'Failed to get profile' });
    }
});

module.exports = router;
