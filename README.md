# AirForShare - Local Network File Sharing

A Python-based AirDrop-like file sharing application that enables seamless file transfer between devices on the same local network, even without internet access.

## Features

- 🔍 **Auto Device Discovery** - Automatically find nearby devices on your network
- 📁 **Drag & Drop Interface** - Simple file sharing with intuitive UI
- 🌐 **Cross-Platform** - Works on Windows, macOS, and Linux
- 🔒 **Private Network** - No internet required, works on local networks
- ⚡ **Fast Transfer** - Direct peer-to-peer file transfer
- 🔐 **Secure** - Local network only, no cloud storage

## Quick Start

```bash
# Install dependencies
pip install -r requirements.txt

# Run the application
python main.py
```

## Requirements

- Python 3.8+
- Local network connection (WiFi/Ethernet)
- Compatible operating system (Windows/macOS/Linux)

## How It Works

1. **Discovery**: Uses multicast to find devices running AirForShare
2. **Connection**: Establishes direct TCP connection between devices
3. **Transfer**: Sends files directly device-to-device
4. **Notification**: Shows transfer progress and completion status

## Network Requirements

- Devices must be on the same subnet
- Multicast must be enabled on the network
- Firewall may need configuration for initial setup

## License

MIT License - See LICENSE file for details
