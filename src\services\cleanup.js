/**
 * Cleanup service for expired files, sessions, and temporary data
 */

const fileManager = require('./fileManager');
const authService = require('./auth');
const logger = require('../utils/logger');
const config = require('../config');

class CleanupService {
    constructor() {
        this.isRunning = false;
    }

    async cleanupExpiredFiles() {
        if (!config.development.disableCleanup) {
            try {
                const deletedCount = await fileManager.cleanupExpiredFiles();
                if (deletedCount > 0) {
                    logger.info(`Cleanup: Removed ${deletedCount} expired files`);
                }
                return deletedCount;
            } catch (error) {
                logger.error('Error during file cleanup:', error);
                return 0;
            }
        }
        return 0;
    }

    cleanupExpiredSessions() {
        try {
            authService.cleanup();
            logger.debug('Cleanup: Expired sessions and rooms cleaned up');
        } catch (error) {
            logger.error('Error during session cleanup:', error);
        }
    }

    async runFullCleanup() {
        if (this.isRunning) {
            logger.warn('Cleanup already in progress, skipping...');
            return;
        }

        this.isRunning = true;
        logger.info('Starting full cleanup...');

        try {
            // Cleanup expired files
            const deletedFiles = await this.cleanupExpiredFiles();
            
            // Cleanup expired sessions and rooms
            this.cleanupExpiredSessions();

            logger.info(`Full cleanup completed. Files removed: ${deletedFiles}`);

        } catch (error) {
            logger.error('Error during full cleanup:', error);
        } finally {
            this.isRunning = false;
        }
    }

    getCleanupStats() {
        const fileStats = fileManager.getFileStats();
        
        return {
            totalFiles: fileStats.totalFiles,
            totalSizeMB: fileStats.totalSizeMB,
            lastCleanup: new Date().toISOString(),
            cleanupInterval: config.cleanup.cleanupInterval,
            fileExpiryTime: config.cleanup.expiryTime
        };
    }
}

// Create singleton instance
const cleanupService = new CleanupService();

module.exports = cleanupService;
