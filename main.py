#!/usr/bin/env python3
"""
AirForShare - Local Network File Sharing Application
Main entry point for the application
"""

import sys
import tkinter as tk
from tkinter import messagebox
import threading
import logging
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from gui.main_window import MainWindow
from core.discovery import DeviceDiscovery
from core.file_server import FileServer
from utils.logger import setup_logger

def main():
    """Main entry point for AirForShare application"""
    
    # Setup logging
    setup_logger()
    logger = logging.getLogger(__name__)
    
    try:
        # Create main application window
        root = tk.Tk()
        root.title("AirForShare - Local File Sharing")
        root.geometry("800x600")
        root.minsize(600, 400)
        
        # Initialize core components
        discovery = DeviceDiscovery()
        file_server = FileServer()
        
        # Create main application window
        app = MainWindow(root, discovery, file_server)
        
        # Start background services
        def start_services():
            try:
                discovery.start()
                file_server.start()
                logger.info("AirForShare services started successfully")
            except Exception as e:
                logger.error(f"Failed to start services: {e}")
                messagebox.showerror("Error", f"Failed to start services: {e}")
        
        # Start services in background thread
        service_thread = threading.Thread(target=start_services, daemon=True)
        service_thread.start()
        
        # Handle application shutdown
        def on_closing():
            logger.info("Shutting down AirForShare...")
            discovery.stop()
            file_server.stop()
            root.destroy()
        
        root.protocol("WM_DELETE_WINDOW", on_closing)
        
        # Start GUI event loop
        logger.info("Starting AirForShare GUI...")
        root.mainloop()
        
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        if 'root' in locals():
            messagebox.showerror("Fatal Error", f"Application error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
