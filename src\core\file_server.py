"""
File server module for handling file transfers
Manages HTTP server for receiving files and client connections for sending
"""

import os
import socket
import threading
import json
import logging
import hashlib
from pathlib import Path
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import parse_qs, urlparse
import cgi
import tempfile
from typing import Dict, Callable, Optional

class FileServer:
    """HTTP server for handling file transfers"""
    
    def __init__(self, port: int = 8888):
        self.port = port
        self.server = None
        self.server_thread = None
        self.running = False
        self.logger = logging.getLogger(__name__)
        self.upload_callbacks: list = []
        self.download_dir = Path.home() / "Downloads" / "AirForShare"
        self.download_dir.mkdir(parents=True, exist_ok=True)
        
    def add_upload_callback(self, callback: Callable):
        """Add callback for when files are uploaded"""
        self.upload_callbacks.append(callback)
        
    def _notify_upload_callbacks(self, file_info: dict):
        """Notify callbacks about file uploads"""
        for callback in self.upload_callbacks:
            try:
                callback(file_info)
            except Exception as e:
                self.logger.error(f"Error in upload callback: {e}")
    
    def start(self):
        """Start the file server"""
        if self.running:
            return
            
        try:
            # Create custom handler class with access to server instance
            handler_class = type('AirForShareHandler', 
                               (AirForShareHTTPHandler,), 
                               {'file_server': self})
            
            self.server = HTTPServer(('', self.port), handler_class)
            self.running = True
            
            # Start server in separate thread
            self.server_thread = threading.Thread(target=self.server.serve_forever, daemon=True)
            self.server_thread.start()
            
            self.logger.info(f"File server started on port {self.port}")
            
        except Exception as e:
            self.logger.error(f"Failed to start file server: {e}")
            self.running = False
            raise
    
    def stop(self):
        """Stop the file server"""
        if not self.running:
            return
            
        try:
            self.running = False
            
            if self.server:
                self.server.shutdown()
                self.server.server_close()
                
            if self.server_thread:
                self.server_thread.join(timeout=5)
                
            self.logger.info("File server stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping file server: {e}")
    
    def send_file(self, file_path: str, target_address: str, target_port: int) -> bool:
        """Send a file to another device"""
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                self.logger.error(f"File not found: {file_path}")
                return False
            
            # Calculate file hash for integrity check
            file_hash = self._calculate_file_hash(file_path)
            
            # Prepare file info
            file_info = {
                'name': file_path.name,
                'size': file_path.stat().st_size,
                'hash': file_hash
            }
            
            # Send file using HTTP POST
            success = self._upload_file(file_path, file_info, target_address, target_port)
            
            if success:
                self.logger.info(f"Successfully sent file: {file_path.name}")
            else:
                self.logger.error(f"Failed to send file: {file_path.name}")
                
            return success
            
        except Exception as e:
            self.logger.error(f"Error sending file: {e}")
            return False
    
    def _calculate_file_hash(self, file_path: Path) -> str:
        """Calculate SHA256 hash of file for integrity verification"""
        hash_sha256 = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()
    
    def _upload_file(self, file_path: Path, file_info: dict, address: str, port: int) -> bool:
        """Upload file to target device using HTTP POST"""
        try:
            import requests
            
            url = f"http://{address}:{port}/upload"
            
            with open(file_path, 'rb') as f:
                files = {'file': (file_info['name'], f, 'application/octet-stream')}
                data = {
                    'file_info': json.dumps(file_info)
                }
                
                response = requests.post(url, files=files, data=data, timeout=30)
                return response.status_code == 200
                
        except Exception as e:
            self.logger.error(f"Error uploading file: {e}")
            return False


class AirForShareHTTPHandler(BaseHTTPRequestHandler):
    """HTTP request handler for file transfers"""
    
    def log_message(self, format, *args):
        """Override to use our logger instead of stderr"""
        logger = logging.getLogger(__name__)
        logger.info(f"{self.address_string()} - {format % args}")
    
    def do_GET(self):
        """Handle GET requests - serve file download page or file list"""
        try:
            if self.path == '/':
                self._serve_main_page()
            elif self.path == '/api/status':
                self._serve_status()
            else:
                self.send_error(404, "Not Found")
        except Exception as e:
            logging.getLogger(__name__).error(f"Error in GET handler: {e}")
            self.send_error(500, "Internal Server Error")
    
    def do_POST(self):
        """Handle POST requests - file uploads"""
        try:
            if self.path == '/upload':
                self._handle_file_upload()
            else:
                self.send_error(404, "Not Found")
        except Exception as e:
            logging.getLogger(__name__).error(f"Error in POST handler: {e}")
            self.send_error(500, "Internal Server Error")
    
    def _serve_main_page(self):
        """Serve a simple status page"""
        html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>AirForShare Device</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                .container { max-width: 600px; margin: 0 auto; }
                .status { background: #e8f5e8; padding: 20px; border-radius: 8px; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🚀 AirForShare Device</h1>
                <div class="status">
                    <h3>Device Status: Online</h3>
                    <p>This device is ready to receive files via AirForShare.</p>
                    <p>Use the AirForShare application to send files to this device.</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html')
        self.end_headers()
        self.wfile.write(html.encode())
    
    def _serve_status(self):
        """Serve API status endpoint"""
        status = {
            'status': 'online',
            'service': 'AirForShare',
            'version': '1.0'
        }
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(status).encode())
    
    def _handle_file_upload(self):
        """Handle file upload from other devices"""
        try:
            # Parse multipart form data
            content_type = self.headers.get('Content-Type', '')
            if not content_type.startswith('multipart/form-data'):
                self.send_error(400, "Expected multipart/form-data")
                return
            
            # Parse the multipart data
            form = cgi.FieldStorage(
                fp=self.rfile,
                headers=self.headers,
                environ={'REQUEST_METHOD': 'POST'}
            )
            
            # Get file info
            file_info_str = form.getfirst('file_info', '{}')
            file_info = json.loads(file_info_str)
            
            # Get uploaded file
            file_field = form['file']
            if not file_field.filename:
                self.send_error(400, "No file uploaded")
                return
            
            # Save file to download directory
            download_dir = self.file_server.download_dir
            safe_filename = self._make_safe_filename(file_field.filename)
            file_path = download_dir / safe_filename
            
            # Handle duplicate filenames
            counter = 1
            original_path = file_path
            while file_path.exists():
                stem = original_path.stem
                suffix = original_path.suffix
                file_path = original_path.parent / f"{stem}_{counter}{suffix}"
                counter += 1
            
            # Write file data
            with open(file_path, 'wb') as f:
                f.write(file_field.file.read())
            
            # Verify file integrity if hash provided
            if 'hash' in file_info:
                calculated_hash = self.file_server._calculate_file_hash(file_path)
                if calculated_hash != file_info['hash']:
                    logging.getLogger(__name__).warning(f"File hash mismatch for {file_path}")
            
            # Notify callbacks
            received_file_info = {
                'name': file_path.name,
                'path': str(file_path),
                'size': file_path.stat().st_size,
                'sender': self.client_address[0]
            }
            
            self.file_server._notify_upload_callbacks(received_file_info)
            
            # Send success response
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            
            response = {
                'status': 'success',
                'message': 'File uploaded successfully',
                'filename': file_path.name
            }
            self.wfile.write(json.dumps(response).encode())
            
            logging.getLogger(__name__).info(f"Received file: {file_path.name} from {self.client_address[0]}")
            
        except Exception as e:
            logging.getLogger(__name__).error(f"Error handling file upload: {e}")
            self.send_error(500, "Upload failed")
    
    def _make_safe_filename(self, filename: str) -> str:
        """Make filename safe for filesystem"""
        # Remove or replace unsafe characters
        unsafe_chars = '<>:"/\\|?*'
        safe_name = filename
        for char in unsafe_chars:
            safe_name = safe_name.replace(char, '_')
        
        # Limit length
        if len(safe_name) > 255:
            name, ext = os.path.splitext(safe_name)
            safe_name = name[:255-len(ext)] + ext
            
        return safe_name
