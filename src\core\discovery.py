"""
Device discovery module using multicast DNS and Zeroconf
Handles finding and announcing AirForShare devices on the local network
"""

import socket
import threading
import time
import logging
import json
from typing import Dict, List, Callable
from zeroconf import ServiceInfo, Zeroconf, ServiceListener, ServiceBrowser

try:
    import netifaces
    HAS_NETIFACES = True
except ImportError:
    HAS_NETIFACES = False
    logging.warning("netifaces not available, using fallback network detection")

class DeviceDiscovery:
    """Handles device discovery and announcement on local network"""
    
    SERVICE_TYPE = "_airforshare._tcp.local."
    
    def __init__(self, device_name: str = None):
        self.logger = logging.getLogger(__name__)
        self.device_name = device_name or socket.gethostname()
        self.port = 8888
        self.zeroconf = None
        self.service_info = None
        self.browser = None
        self.discovered_devices: Dict[str, dict] = {}
        self.callbacks: List[Callable] = []
        self.running = False
        
    def add_device_callback(self, callback: Callable):
        """Add callback for when devices are discovered or removed"""
        self.callbacks.append(callback)
        
    def _notify_callbacks(self, device_info: dict, action: str):
        """Notify all callbacks about device changes"""
        for callback in self.callbacks:
            try:
                callback(device_info, action)
            except Exception as e:
                self.logger.error(f"Error in device callback: {e}")
    
    def get_local_ip(self) -> str:
        """Get the local IP address for network communication"""
        if HAS_NETIFACES:
            try:
                # Get default gateway interface
                gateways = netifaces.gateways()
                default_interface = gateways['default'][netifaces.AF_INET][1]
                
                # Get IP of default interface
                addr_info = netifaces.ifaddresses(default_interface)
                local_ip = addr_info[netifaces.AF_INET][0]['addr']
                return local_ip
            except Exception as e:
                self.logger.warning(f"Could not get local IP via netifaces: {e}")
        
        # Fallback method when netifaces is not available
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            return local_ip
        except Exception as e:
            self.logger.error(f"Could not determine local IP: {e}")
            return "127.0.0.1"
    
    def start(self):
        """Start device discovery and announcement"""
        if self.running:
            return
            
        try:
            self.running = True
            self.zeroconf = Zeroconf()
            
            # Register our service
            local_ip = self.get_local_ip()
            service_name = f"{self.device_name}.{self.SERVICE_TYPE}"
            
            self.service_info = ServiceInfo(
                self.SERVICE_TYPE,
                service_name,
                addresses=[socket.inet_aton(local_ip)],
                port=self.port,
                properties={
                    'device_name': self.device_name.encode('utf-8'),
                    'version': b'1.0',
                    'platform': socket.platform.encode('utf-8') if hasattr(socket, 'platform') else b'unknown'
                }
            )
            
            self.zeroconf.register_service(self.service_info)
            self.logger.info(f"Registered service: {service_name} on {local_ip}:{self.port}")
            
            # Start browsing for other devices
            listener = AirForShareListener(self)
            self.browser = ServiceBrowser(self.zeroconf, self.SERVICE_TYPE, listener)
            
            self.logger.info("Device discovery started")
            
        except Exception as e:
            self.logger.error(f"Failed to start device discovery: {e}")
            self.running = False
            raise
    
    def stop(self):
        """Stop device discovery and announcement"""
        if not self.running:
            return
            
        try:
            self.running = False
            
            if self.browser:
                self.browser.cancel()
                self.browser = None
                
            if self.service_info and self.zeroconf:
                self.zeroconf.unregister_service(self.service_info)
                self.service_info = None
                
            if self.zeroconf:
                self.zeroconf.close()
                self.zeroconf = None
                
            self.discovered_devices.clear()
            self.logger.info("Device discovery stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping device discovery: {e}")
    
    def get_discovered_devices(self) -> Dict[str, dict]:
        """Get list of currently discovered devices"""
        return self.discovered_devices.copy()


class AirForShareListener(ServiceListener):
    """Zeroconf service listener for AirForShare devices"""
    
    def __init__(self, discovery: DeviceDiscovery):
        self.discovery = discovery
        self.logger = logging.getLogger(__name__)
    
    def add_service(self, zc: Zeroconf, type_: str, name: str) -> None:
        """Called when a new AirForShare service is discovered"""
        try:
            info = zc.get_service_info(type_, name)
            if info:
                device_info = self._parse_service_info(info)
                if device_info and device_info['name'] != self.discovery.device_name:
                    self.discovery.discovered_devices[name] = device_info
                    self.discovery._notify_callbacks(device_info, 'added')
                    self.logger.info(f"Discovered device: {device_info['name']} at {device_info['address']}")
        except Exception as e:
            self.logger.error(f"Error adding service {name}: {e}")
    
    def remove_service(self, zc: Zeroconf, type_: str, name: str) -> None:
        """Called when an AirForShare service is removed"""
        if name in self.discovery.discovered_devices:
            device_info = self.discovery.discovered_devices.pop(name)
            self.discovery._notify_callbacks(device_info, 'removed')
            self.logger.info(f"Device removed: {device_info['name']}")
    
    def update_service(self, zc: Zeroconf, type_: str, name: str) -> None:
        """Called when an AirForShare service is updated"""
        try:
            info = zc.get_service_info(type_, name)
            if info:
                device_info = self._parse_service_info(info)
                if device_info and device_info['name'] != self.discovery.device_name:
                    self.discovery.discovered_devices[name] = device_info
                    self.discovery._notify_callbacks(device_info, 'updated')
        except Exception as e:
            self.logger.error(f"Error updating service {name}: {e}")
    
    def _parse_service_info(self, info: ServiceInfo) -> dict:
        """Parse Zeroconf service info into device information"""
        try:
            address = socket.inet_ntoa(info.addresses[0]) if info.addresses else None
            properties = info.properties or {}
            
            device_info = {
                'name': properties.get('device_name', b'Unknown').decode('utf-8'),
                'address': address,
                'port': info.port,
                'version': properties.get('version', b'1.0').decode('utf-8'),
                'platform': properties.get('platform', b'unknown').decode('utf-8'),
                'service_name': info.name
            }
            
            return device_info
        except Exception as e:
            self.logger.error(f"Error parsing service info: {e}")
            return None
