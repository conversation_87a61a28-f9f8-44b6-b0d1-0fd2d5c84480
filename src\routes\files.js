/**
 * File upload and download routes
 */

const express = require('express');
const multer = require('multer');
const router = express.Router();
const fileManager = require('../services/fileManager');
const authService = require('../services/auth');
const config = require('../config');
const logger = require('../utils/logger');

// Configure multer for file uploads
const storage = multer.memoryStorage();
const upload = multer({
    storage: storage,
    limits: {
        fileSize: Math.max(...Object.values(config.upload.maxFileSize)) // Use the largest limit
    },
    fileFilter: (req, file, cb) => {
        // Check blocked file types
        const path = require('path');
        const extension = path.extname(file.originalname).toLowerCase();
        
        if (config.upload.blockedTypes.includes(extension)) {
            return cb(new Error(`File type ${extension} is not allowed`), false);
        }
        
        // Check allowed file types (if specified)
        if (config.upload.allowedTypes.length > 0 && !config.upload.allowedTypes.includes(extension)) {
            return cb(new Error(`File type ${extension} is not allowed`), false);
        }
        
        cb(null, true);
    }
});

// Upload file endpoint
router.post('/upload', upload.single('file'), async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({ error: 'No file provided' });
        }

        // Determine user type and ID
        let userType = 'guest';
        let userId = 'anonymous';

        // Check for authentication
        const authHeader = req.headers.authorization;
        if (authHeader && authHeader.startsWith('Bearer ')) {
            const token = authHeader.substring(7);
            try {
                const user = authService.verifyToken(token);
                userType = user.userType;
                userId = user.username;
            } catch (error) {
                try {
                    const session = authService.verifyGuestSession(token);
                    userType = 'guest';
                    userId = session.sessionId;
                } catch (sessionError) {
                    // Continue as anonymous guest
                }
            }
        }

        // Check file size against user limits
        const maxSize = config.upload.maxFileSize[userType] || config.upload.maxFileSize.guest;
        if (req.file.size > maxSize) {
            return res.status(413).json({ 
                error: `File size exceeds limit for ${userType} users (${Math.round(maxSize / 1024 / 1024)}MB)` 
            });
        }

        // Save file
        const metadata = await fileManager.saveFile(req.file.buffer, req.file.originalname, userType, userId);

        // Broadcast file upload event
        if (req.app.get('io')) {
            req.app.get('io').emit('fileUploaded', {
                id: metadata.id,
                name: metadata.originalName,
                size: metadata.size,
                uploadedBy: metadata.uploadedBy,
                uploadedAt: metadata.uploadedAt
            });
        }

        logger.info(`File uploaded: ${metadata.originalName} by ${userId} (${userType})`);

        res.json({
            id: metadata.id,
            name: metadata.originalName,
            size: metadata.size,
            mimeType: metadata.mimeType,
            uploadedAt: metadata.uploadedAt,
            expiresAt: metadata.expiresAt,
            message: 'File uploaded successfully'
        });

    } catch (error) {
        logger.error('Error uploading file:', error);
        
        if (error.code === 'LIMIT_FILE_SIZE') {
            return res.status(413).json({ error: 'File too large' });
        }
        
        res.status(500).json({ error: error.message || 'Failed to upload file' });
    }
});

// Download file endpoint
router.get('/download/:id', async (req, res) => {
    try {
        const fileId = req.params.id;
        const fileData = await fileManager.getFile(fileId);

        if (!fileData) {
            return res.status(404).json({ error: 'File not found' });
        }

        const { metadata, buffer } = fileData;

        // Set appropriate headers
        res.setHeader('Content-Type', metadata.mimeType);
        res.setHeader('Content-Length', metadata.size);
        res.setHeader('Content-Disposition', `attachment; filename="${metadata.originalName}"`);
        res.setHeader('Cache-Control', 'no-cache');

        // Send file
        res.send(buffer);

        logger.info(`File downloaded: ${metadata.originalName} (${fileId})`);

    } catch (error) {
        logger.error('Error downloading file:', error);
        
        if (error.message === 'File not found' || error.message === 'File has expired') {
            return res.status(404).json({ error: error.message });
        }
        
        res.status(500).json({ error: 'Failed to download file' });
    }
});

// Get file info endpoint
router.get('/info/:id', async (req, res) => {
    try {
        const fileId = req.params.id;
        const fileData = await fileManager.getFile(fileId);

        if (!fileData) {
            return res.status(404).json({ error: 'File not found' });
        }

        const { metadata } = fileData;

        // Return metadata without the file content
        res.json({
            id: metadata.id,
            name: metadata.originalName,
            size: metadata.size,
            mimeType: metadata.mimeType,
            uploadedAt: metadata.uploadedAt,
            lastAccessed: metadata.lastAccessed,
            accessCount: metadata.accessCount,
            expiresAt: metadata.expiresAt
        });

    } catch (error) {
        logger.error('Error getting file info:', error);
        
        if (error.message === 'File not found' || error.message === 'File has expired') {
            return res.status(404).json({ error: error.message });
        }
        
        res.status(500).json({ error: 'Failed to get file info' });
    }
});

// Delete file endpoint
router.delete('/:id', async (req, res) => {
    try {
        const fileId = req.params.id;
        const success = await fileManager.deleteFile(fileId);

        if (success) {
            // Broadcast file deletion event
            if (req.app.get('io')) {
                req.app.get('io').emit('fileDeleted', { id: fileId });
            }

            res.json({ message: 'File deleted successfully' });
        } else {
            res.status(404).json({ error: 'File not found' });
        }

    } catch (error) {
        logger.error('Error deleting file:', error);
        res.status(500).json({ error: 'Failed to delete file' });
    }
});

// Multiple file upload endpoint
router.post('/upload-multiple', upload.array('files', 10), async (req, res) => {
    try {
        if (!req.files || req.files.length === 0) {
            return res.status(400).json({ error: 'No files provided' });
        }

        // Determine user type and ID
        let userType = 'guest';
        let userId = 'anonymous';

        const authHeader = req.headers.authorization;
        if (authHeader && authHeader.startsWith('Bearer ')) {
            const token = authHeader.substring(7);
            try {
                const user = authService.verifyToken(token);
                userType = user.userType;
                userId = user.username;
            } catch (error) {
                try {
                    const session = authService.verifyGuestSession(token);
                    userType = 'guest';
                    userId = session.sessionId;
                } catch (sessionError) {
                    // Continue as anonymous guest
                }
            }
        }

        // Check simultaneous upload limits
        const maxUploads = config.upload.maxSimultaneousUploads[userType] || config.upload.maxSimultaneousUploads.guest;
        if (req.files.length > maxUploads) {
            return res.status(413).json({ 
                error: `Too many files. ${userType} users can upload maximum ${maxUploads} files at once` 
            });
        }

        const results = [];
        const errors = [];

        for (const file of req.files) {
            try {
                // Check file size
                const maxSize = config.upload.maxFileSize[userType] || config.upload.maxFileSize.guest;
                if (file.size > maxSize) {
                    errors.push({
                        filename: file.originalname,
                        error: `File size exceeds limit (${Math.round(maxSize / 1024 / 1024)}MB)`
                    });
                    continue;
                }

                const metadata = await fileManager.saveFile(file.buffer, file.originalname, userType, userId);
                
                results.push({
                    id: metadata.id,
                    name: metadata.originalName,
                    size: metadata.size,
                    mimeType: metadata.mimeType,
                    uploadedAt: metadata.uploadedAt,
                    expiresAt: metadata.expiresAt
                });

                // Broadcast individual file upload event
                if (req.app.get('io')) {
                    req.app.get('io').emit('fileUploaded', {
                        id: metadata.id,
                        name: metadata.originalName,
                        size: metadata.size,
                        uploadedBy: metadata.uploadedBy,
                        uploadedAt: metadata.uploadedAt
                    });
                }

            } catch (error) {
                errors.push({
                    filename: file.originalname,
                    error: error.message
                });
            }
        }

        logger.info(`Multiple files uploaded: ${results.length} successful, ${errors.length} failed by ${userId} (${userType})`);

        res.json({
            successful: results,
            errors: errors,
            message: `${results.length} files uploaded successfully, ${errors.length} failed`
        });

    } catch (error) {
        logger.error('Error uploading multiple files:', error);
        res.status(500).json({ error: 'Failed to upload files' });
    }
});

module.exports = router;
